# 🏗️ **Cache Architecture Analysis - TrueStore Frontend**

## 📋 **Document Overview**

| Attribute | Value |
|-----------|-------|
| **System** | TrueStore Frontend Cache Layer |
| **Current Scale** | 10,000 CCU, 100+ Domains |
| **Technology** | Next.js 14, Redis, unstable_cache |
| **Problem** | Cannot invalidate cache for single domain |
| **Status** | 🚨 **CRITICAL** |

---

## 1️⃣ **KIẾN TRÚC HIỆN TẠI**

### **Cache Implementation**
```typescript
// packages/common/platform/get-platform-config.ts
export const getPlatformConfig = cache(
  async (domain: string) => {
    const [platform, commonPixelIds] = await Promise.all([
      ky.get(`PXTRUE2/${domain}/.json`).json<PlatformConfig>(),
      getPlatformPixel(),
    ]);
    return processedPlatform;
  },
  [],
  {
    revalidate: 86400,
    tags: ['platform-config', 'all'], // ❌ Static tags
  },
);

// packages/model/product/get-product.ts
export const getProduct = cache(
  async (domain: string, slug: string, params?: GetProductParams) => {
    const client = await createPlatformClient(domain);
    // ... fetch product data
    return processedProduct;
  },
  ['product', 'domain', 'slug'],
  {
    revalidate: 86400,
    tags: ['product', 'all'], // ❌ Static tags
  },
);
```

### **Cache Invalidation**
```typescript
// app/api/revalidate/route.ts
export async function GET(request: NextRequest) {
  const tag = request.nextUrl.searchParams.get('tag');
  
  if (tag) {
    revalidateTag(tag); // ❌ Affects ALL domains
  }
  
  return NextResponse.json({ revalidated: true });
}
```

### **Current Cache Tags**
- `'all'` - Nuclear option, clears everything
- `'platform-config'` - All platform configs (100+ domains)
- `'product'` - All products (all domains)
- `'reviews'` - All reviews (all domains)
- `'platform-pixel'` - Global pixel data

---

## 2️⃣ **VẤN ĐỀ SẼ GẶP**

### **🚨 Critical Problem: Cannot Invalidate Single Domain**

**Scenario 1: Update Firebase config cho domain1.com**
```typescript
// ❌ CURRENT: Must clear ALL domains
revalidateTag('platform-config'); // Affects 100+ domains
// → Cache storm for ALL domains
// → 200+ Firebase API calls
// → 2-5s response time for ALL users
```

**Scenario 2: Update product price cho domain1.com**
```typescript
// ❌ CURRENT: Must clear ALL products
revalidateTag('product'); // Affects all domains
// → All product cache invalid
// → Massive API calls to Platform service
// → Performance degradation across ALL domains
```

**Scenario 3: unstable_cache Limitation**
```typescript
// ❌ CANNOT DO THIS - Dynamic tags not supported
export const getPlatformConfig = cache(
  async (domain: string) => { /* ... */ },
  [],
  {
    tags: [`platform:${domain}`], // ❌ Template literals not allowed
  },
);
```

### **Impact Analysis**
| Use Case | Current Solution | Impact | Domains Affected |
|----------|------------------|--------|------------------|
| Update 1 domain config | `revalidateTag('platform-config')` | Cache storm | ALL 100+ |
| Update 1 product price | `revalidateTag('product')` | Cache storm | ALL 100+ |
| Update global pixels | `revalidateTag('all')` | Nuclear | ALL 100+ |
| Deploy new code | `revalidateTag('all')` | Nuclear | ALL 100+ |

### **Business Impact**
- **Revenue Loss**: 25-40% conversion drop during cache storms
- **User Experience**: 2-5s response times
- **Infrastructure**: Over-provisioning to handle spikes
- **Operations**: 20+ hours/month on cache incidents

---

## 3️⃣ **KIẾN TRÚC MỚI**

### **🎯 Solution: Cache Function Factory**

**Core Principle: Domain-Specific Cache Functions**
```typescript
// packages/common/cache/domain-factory.ts
import { unstable_cache as cache, revalidateTag } from 'next/cache';

export const createDomainCacheFactory = <T extends any[], R>(
  fetchFn: (...args: T) => Promise<R>,
  cacheType: string,
  options: { revalidate?: number } = {}
) => {
  return (domain: string) => {
    const cacheKey = `${cacheType}-${domain}`;
    
    return cache(
      fetchFn,
      [cacheKey],
      {
        revalidate: options.revalidate || 86400,
        tags: [cacheKey, cacheType], // Both specific and general
      }
    );
  };
};
```

### **Implementation**

**Platform Config:**
```typescript
// packages/common/platform/get-platform-config.ts
const platformConfigFactory = createDomainCacheFactory(
  async (domain: string) => {
    const [platform, commonPixelIds] = await Promise.all([
      ky.get(`PXTRUE2/${domain}/.json`).json<PlatformConfig>(),
      getPlatformPixel(),
    ]);
    return processedPlatform;
  },
  'platform-config'
);

export const getPlatformConfig = (domain: string) => {
  return platformConfigFactory(domain)(domain);
};
```

**Product Cache:**
```typescript
// packages/model/product/get-product.ts
const productFactory = createDomainCacheFactory(
  async (domain: string, slug: string, params?: GetProductParams) => {
    const client = await createPlatformClient(domain);
    // ... fetch product data
    return processedProduct;
  },
  'product'
);

export const getProduct = (domain: string, slug: string, params?: GetProductParams) => {
  return productFactory(domain)(domain, slug, params);
};
```

### **Cache Invalidation**

**Granular Control:**
```typescript
// Clear specific domain only
revalidateTag('platform-config-domain1.com'); // ✅ Only domain1.com

// Clear all platform configs (if needed)
revalidateTag('platform-config'); // ✅ All domains

// Clear specific product
revalidateTag('product-domain1.com'); // ✅ Only domain1.com products
```

**New Invalidation API:**
```typescript
// app/api/cache/domain/route.ts
export async function POST(request: NextRequest) {
  const { domain, action } = await request.json();
  
  switch (action) {
    case 'clear_platform':
      revalidateTag(`platform-config-${domain}`);
      break;
    case 'clear_products':
      revalidateTag(`product-${domain}`);
      break;
    case 'clear_domain':
      revalidateTag(`platform-config-${domain}`);
      revalidateTag(`product-${domain}`);
      revalidateTag(`reviews-${domain}`);
      break;
  }
  
  return NextResponse.json({ success: true, domain, action });
}
```

### **Expected Results**

| Use Case | New Solution | Impact | Domains Affected |
|----------|--------------|--------|------------------|
| Update 1 domain config | `revalidateTag('platform-config-domain1.com')` | Isolated | 1 only |
| Update 1 product price | `revalidateTag('product-domain1.com')` | Isolated | 1 only |
| Update global pixels | `revalidateTag('platform-pixel')` | Controlled | ALL (but minimal) |
| Deploy new code | Selective invalidation | Controlled | As needed |

### **Performance Targets**
- **Cache hit rate**: 95%+ (vs current 60-70%)
- **Response time**: <100ms (vs current 2-5s during storms)
- **Error rate**: <1% (vs current 15-30% during storms)
- **Recovery time**: <5s (vs current 30-60s)

---

## 🎯 **IMPLEMENTATION PLAN**

### **Phase 1: Core Implementation (1-2 days)**
1. Create `domain-factory.ts` utility
2. Refactor `getPlatformConfig` to use factory
3. Refactor `getProduct` to use factory
4. Test domain-specific invalidation

### **Phase 2: API & Monitoring (1 day)**
1. Create `/api/cache/domain` endpoint
2. Add cache operation logging
3. Update existing invalidation calls

### **Phase 3: Rollout (1 day)**
1. Deploy to staging
2. Load testing
3. Production deployment
4. Monitor cache performance

**Total Implementation Time: 3-4 days**
**Risk Level: Low (backward compatible)**
**Expected Impact: Eliminates cache storms, improves performance for all domains**
