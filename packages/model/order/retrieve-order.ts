import {notFound} from 'next/navigation';

import {HTTPError} from 'ky';

import {createNodePublicClient} from '@common/platform/ssr';

import type {Order} from './typings';

interface RetrieveProductParams {
  throwNotFound?: boolean;
}

export async function retrieveOrder(
  domain: string,
  id: string | number,
  additionalKey: string,
  params?: RetrieveProductParams,
) {
  const client = await createNodePublicClient(domain);
  try {
    const order = await client
      .get(`v3/orders/check/${id}`, {
        cache: 'no-store',
        searchParams: {
          key: additionalKey,
        },
        hooks: {
          afterResponse: [
            async (request: any, options: any, response: any) => {
              if (!response.ok) {
                return response;
              }

              const order: Order = await response.json();
              if (order.order_key !== additionalKey) {
                throw new HTTPError(
                  new Response(null, {
                    status: 404,
                    headers: response.headers,
                  }),
                  request,
                  options,
                );
              }

              // Return new response with the same data
              return new Response(JSON.stringify(order), {
                headers: response.headers,
                status: response.status,
              });
            },
          ],
        },
      })
      .json<Order>();

    return order;
  } catch (error) {
    if (error instanceof HTTPError) {
      if (params?.throwNotFound && error.response.status === 404) {
        notFound();
      }
    }
    throw error;
  }
}
