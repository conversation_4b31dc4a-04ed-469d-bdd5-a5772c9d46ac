import {revalidatePath, revalidateTag} from 'next/cache';
import {NextRequest, NextResponse} from 'next/server';

export async function GET(request: NextRequest) {
  const tag = request.nextUrl.searchParams.get('tag');
  const path = request.nextUrl.searchParams.get('path');
  const domain = request.nextUrl.searchParams.get('domain');
  const action = request.nextUrl.searchParams.get('action');

  let revalidatedItems: string[] = [];

  // 1. Clear tất cả cache của 1 domain cụ thể
  if (action === 'clear_domain' && domain) {
    // Clear all domain-specific pages
    revalidatePath(`/${domain}`, 'layout'); // Clear layout cache
    revalidatePath(`/mobile/${domain}`, 'layout'); // Clear mobile layout

    // Clear all pages for this domain
    revalidatePath(`/${domain}`, 'page');
    revalidatePath(`/mobile/${domain}`, 'page');

    // Clear cache tags (this will affect all domains, but necessary)
    revalidateTag('platform-config'); // Platform config cache
    revalidateTag('product'); // Product cache
    revalidateTag('reviews'); // Reviews cache

    revalidatedItems.push(`All cache for domain: ${domain}`);
    console.log(`[CACHE] Cleared all cache for domain: ${domain}`);
  }

  // 2. Clear config của tất cả domains
  if (action === 'clear_config_all') {
    revalidateTag('platform-config'); // Clear all platform configs
    revalidateTag('platform-pixel'); // Clear global pixels if needed

    revalidatedItems.push('All platform configs cleared');
    console.log('[CACHE] Cleared all platform configs');
  }

  // Original functionality
  if (tag) {
    revalidateTag(tag);
    revalidatedItems.push(`Tag: ${tag}`);
  }

  if (path) {
    revalidatePath(path);
    revalidatedItems.push(`Path: ${path}`);
  }

  const response = NextResponse.json({
    revalidated: true,
    now: Date.now(),
    domain,
    action,
    items: revalidatedItems,
  });

  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS',
  );
  response.headers.set(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization',
  );

  return response;
}

export async function OPTIONS() {
  const response = new NextResponse(null, {status: 200});

  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS',
  );
  response.headers.set(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization',
  );

  return response;
}
