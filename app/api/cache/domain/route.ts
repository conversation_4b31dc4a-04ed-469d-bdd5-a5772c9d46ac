import {revalidatePath, revalidateTag} from 'next/cache';
import {NextRequest, NextResponse} from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { domain, action } = await request.json();

    if (!domain) {
      return new Response('Domain is required', { status: 400 });
    }

    const results: string[] = [];

    switch (action) {
      case 'clear_domain':
        // Clear all cache for specific domain
        await clearDomainCache(domain);
        results.push(`Cleared all cache for domain: ${domain}`);
        break;

      case 'clear_platform':
        // Clear only platform config for specific domain
        await clearDomainPlatformCache(domain);
        results.push(`Cleared platform config for domain: ${domain}`);
        break;

      case 'clear_products':
        // Clear only products for specific domain
        await clearDomainProductCache(domain);
        results.push(`Cleared products for domain: ${domain}`);
        break;

      default:
        return new Response('Invalid action', { status: 400 });
    }

    // Audit logging
    console.log(`[DOMAIN_CACHE] ${new Date().toISOString()}: ${action} for ${domain}`);

    return NextResponse.json({
      success: true,
      domain,
      action,
      results,
      timestamp: Date.now(),
    });

  } catch (error) {
    console.error('[DOMAIN_CACHE_ERROR]', error);
    return new Response('Internal error', { status: 500 });
  }
}

// Global config clear endpoint
export async function GET(request: NextRequest) {
  const action = request.nextUrl.searchParams.get('action');

  if (action === 'clear_config_all') {
    // ✅ Clear all platform configs (necessary for global updates)
    revalidateTag('platform-config');
    // ❌ REMOVED: platform-pixel tag (keep it separate)

    console.log('[GLOBAL_CACHE] Cleared all platform configs');

    return NextResponse.json({
      success: true,
      action: 'clear_config_all',
      message: 'Cleared all platform configs',
      timestamp: Date.now(),
    });
  }

  return new Response('Invalid action', { status: 400 });
}

// Helper functions - Clear theo PATH only (không affect other domains)
async function clearDomainCache(domain: string) {
  // ✅ Clear layout cache (chỉ domain này)
  revalidatePath(`/${domain}`, 'layout');
  revalidatePath(`/mobile/${domain}`, 'layout');

  // ✅ Clear all pages (chỉ domain này)
  revalidatePath(`/${domain}`, 'page');
  revalidatePath(`/mobile/${domain}`, 'page');

  // ✅ Clear specific product pages
  revalidatePath(`/${domain}/product`, 'page');
  revalidatePath(`/mobile/${domain}/product`, 'page');

  // ❌ REMOVED: Không clear tags (affects all domains)
  // revalidateTag('platform-config');
  // revalidateTag('product');
  // revalidateTag('reviews');
}

async function clearDomainPlatformCache(domain: string) {
  // ✅ Clear layout cache only (where platform config is used)
  revalidatePath(`/${domain}`, 'layout');
  revalidatePath(`/mobile/${domain}`, 'layout');

  // ❌ REMOVED: Không clear tag
  // revalidateTag('platform-config');
}

async function clearDomainProductCache(domain: string) {
  // ✅ Clear product pages only
  revalidatePath(`/${domain}/product`, 'page');
  revalidatePath(`/mobile/${domain}/product`, 'page');

  // ❌ REMOVED: Không clear tag
  // revalidateTag('product');
}
