import {headers} from 'next/headers';
import {NextRequest, NextResponse} from 'next/server';

import {getErrorMessage, sendErrorAlert} from '@/lib/telegram';
import {getPlatformConfig, undiciRequest} from '@common/platform/ssr';
import * as Sentry from '@sentry/nextjs';

export async function POST(req: NextRequest) {
  const requestStart = Date.now();
  try {
    const parseStart = Date.now();
    const {orderId} = await req.json();

    if (!orderId) {
      return NextResponse.json({error: 'Order ID is required'}, {status: 400});
    }

    const domainStart = Date.now();
    const domain = headers().get('host') ?? '';

    const parseTime = (Date.now() - parseStart) / 1000;
    const domainTime = (Date.now() - domainStart) / 1000;
    console.log(
      `${new Date().toISOString()}: [${domain}] [PayPal ID: ${orderId}] Payment Capture - Parse JSON: ${parseTime}s, Get Domain: ${domainTime}s`,
    );

    const platformStart = Date.now();
    const config = await getPlatformConfig(domain);
    const platformTime = (Date.now() - platformStart) / 1000;
    const platformLog =
      platformTime > 5 ? `🚨 ${platformTime}s 🚨` : `${platformTime}s`;
    console.log(
      `${new Date().toISOString()}: [${domain}] [PayPal ID: ${orderId}] Payment Capture - Platform Config: ${platformLog}`,
    );

    const captureStart = Date.now();
    // Capture the payment
    const undiciRes = await undiciRequest(
      `api/payments/paypal/capture/${orderId}`,
      {
        method: 'POST',
      },
    );
    const captureResponse = await undiciRes.body.json();

    // Extract response info for logging
    const transactionId =
      captureResponse &&
      typeof captureResponse === 'object' &&
      'transaction_id' in captureResponse
        ? captureResponse.transaction_id
        : 'N/A';
    const responseId =
      captureResponse &&
      typeof captureResponse === 'object' &&
      'id' in captureResponse
        ? captureResponse.id
        : 'N/A';

    const captureTime = (Date.now() - captureStart) / 1000;
    const totalTime = (Date.now() - requestStart) / 1000;
    const captureLog =
      captureTime > 5 ? `🚨 ${captureTime}s 🚨` : `${captureTime}s`;
    const totalLog = totalTime > 5 ? `🚨 ${totalTime}s 🚨` : `${totalTime}s`;

    console.log(
      `${new Date().toISOString()}: [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${responseId}] Payment Capture - Backend Call: ${captureLog}`,
    );
    console.log(
      `${new Date().toISOString()}: [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${responseId}] Payment Capture - Total: ${totalLog}`,
    );

    return NextResponse.json(captureResponse);
  } catch (error) {
    const errorTime = (Date.now() - requestStart) / 1000;
    const errorLog = errorTime > 5 ? `🚨 ${errorTime}s 🚨` : `${errorTime}s`;
    console.log(
      `${new Date().toISOString()}: [${headers().get('host') ?? 'unknown'}] Payment Capture ERROR - Total: ${errorLog}`,
    );
    const errorInfo = getErrorMessage(error);
    sendErrorAlert(errorInfo);
    console.error('Error capturing payment:', error);
    Sentry.captureException(error);
    return NextResponse.json({error: 'Internal server error'}, {status: 500});
  }
}
